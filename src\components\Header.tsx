import React from "react";
import { PanelRightClose } from "lucide-react";
import But<PERSON> from "@/components/button";
import { Link } from "react-router-dom";
import { USER_PROFILE_URL } from "@/utils/urls/backend";
import { useSelector } from "react-redux";
import View from "./view";
import ImageComponent from "./ui/ImageComponent";
import Text from "./text";
// import { useSelector } from "react-redux";

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean
}




const Header: React.FC<HeaderProps> = ({ toggleSidebar,sidebarOpen }) => {
  const settingsData = useSelector((state: any) => state.systemSettings.settings);

  return (
    <header className="h-16 border-b border-gray-200 dark:border-slate-700 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md text-slate-900 dark:text-white shadow-sm transition-all duration-200">
      <View className="flex h-full items-center justify-between px-6">
        {
          !sidebarOpen && (
            <Button
          onClick={toggleSidebar}
          className="text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-800 rounded-lg p-2 transition-all duration-200"
          variant="ghost"
        >
          <PanelRightClose size={20} />
        </Button>
          )
        }

        {/* Center space for potential search or breadcrumbs */}
        <View className="flex-1 flex justify-center">
          {/* Future: Add breadcrumbs or search here */}
        </View>

        {/* User Menu & Actions */}
        <View className="flex items-center gap-4">
          {/* Notifications - Future enhancement */}
          {/* <Button
            className="relative p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-800 rounded-lg transition-all duration-200"
            variant="ghost"
          >
            <Bell size={20} />
            <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-red-500"></span>
          </Button> */}

          {/* User Profile */}
          <Link to={USER_PROFILE_URL}>
            <Button
              className="flex items-center gap-3 px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-xl transition-all duration-200"
              variant="ghost"
            >
              {
                settingsData?.profile_image ? (
                  <View className="h-9 w-9 rounded-full overflow-hidden ring-2 ring-slate-200 dark:ring-slate-700">
                    <ImageComponent
                      src={import.meta.env.VITE_APP_URL + settingsData?.profile_image}
                      alt={"User profile image"}
                      className="rounded-full object-cover h-full w-full"
                    />
                  </View>
                ) : (
                  <View className="h-9 w-9 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center ring-2 ring-slate-200 dark:ring-slate-700">
                    <Text as="span" className="text-sm font-semibold text-white">
                     U
                    </Text>
                  </View>
                )
              }
              <View className="hidden md:block text-left">
                <Text as="p" className="text-sm font-medium text-slate-900 dark:text-white">
                  User Profile
                </Text>
                <Text as="p" className="text-xs text-slate-500 dark:text-slate-400">
                  View & Edit
                </Text>
              </View>
            </Button>
          </Link>
        </View>
      </View>
    </header>
  );
};

export default Header;