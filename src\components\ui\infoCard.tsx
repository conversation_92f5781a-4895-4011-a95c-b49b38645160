import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`bg-white dark:bg-card rounded-2xl p-6 shadow-lg dark:shadow-none border border-gray-100 dark:border-border hover:shadow-xl transition-all duration-300 ease-in-out hover:scale-[1.02] relative overflow-hidden ${className}`}
    style={style}
  >
    {/* Background gradient overlay */}
    <View className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent dark:from-card/50 pointer-events-none" />

    <View className="relative z-10">
      <View className="flex items-center justify-between mb-3">
        <Text
          as="h3"
          className={`text-sm font-medium text-slate-600 dark:text-slate-400 uppercase tracking-wide ${
            titleStyle ? titleStyle : ""
          }`}
        >
          {label}
        </Text>
        {icon && (
          <View
            className={`p-3 rounded-xl bg-gradient-to-br from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30 ${
              iconStyle ? iconStyle : ""
            }`}
          >
            {icon}
          </View>
        )}
      </View>

      <Text
        weight="font-bold"
        className={`text-3xl font-bold text-slate-900 dark:text-white mb-1 ${
          isLink ? "text-primary cursor-pointer hover:underline" : ""
        } ${valueStyle ? valueStyle : ""}`}
      >
        {value}
      </Text>

      {subValue && (
        <Text
          className={`text-sm text-slate-500 dark:text-slate-400 ${
            subValueStyle ? subValueStyle : ""
          }`}
        >
          {subValue}
        </Text>
      )}
    </View>
  </View>
);

export default InfoCard;
