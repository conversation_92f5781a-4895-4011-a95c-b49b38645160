@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  font-size: 80%; 
  
}

body, #root {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: auto;
  font-family: "Work Sans", sans-serif;

}

  /* * {
    scrollbar-width: thin;
    scrollbar-color: var(--primary) #1a1a1a;
  }

  *::-webkit-scrollbar {
    width: 8px;
  }

  *::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 2px;
  }

  *::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 5px;
    border: 2px solid #1a1a1a;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: var(--primary-600);
    box-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
  } */

/* Default (Light Mode) */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--primary) #f0f0f0;
}

*::-webkit-scrollbar {
  width: 8px;
}

*::-webkit-scrollbar-track {
  background: var(--primary-10) #f0f0f0;
  border-radius: 1px;
}

*::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 5px;
  border: 2px solid #f0f0f0;
}

*::-webkit-scrollbar-thumb:hover {
  background: var(--primary-600);
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}

/* Dark Mode */
html.dark * {
  scrollbar-color: var(--primary) #1a1a1a;
}

html.dark *::-webkit-scrollbar-track {
  background: #1a1a1a;
}

html.dark *::-webkit-scrollbar-thumb {
  border: 2px solid #1a1a1a;
}


/* src/styles/globals.css */
:root {
    /* Base colors - Clean, modern background */
  --background: #f8f9fa;
  --foreground: #1a1d29;

  /* UI Component backgrounds */
  --card: #ffffff;
  --card-foreground: #1a1d29;
  --popover: #ffffff;
  --popover-foreground: #1a1d29;

  /* Updated color palette to match reference design */
  --primary-base: #6366f1;
  --secondary-base: #10b981;
  --accent-base: #f59e0b;

  /* Primary colors - Modern purple/indigo */
  --primary: var(--primary-base);
  --primary-foreground: #ffffff;
  --primary-bg: #f0f0ff;

  /* Primary color variants - Purple/Indigo gradient */
  --primary-10: var(--primary-10-base, #faf9ff);
  --primary-20: var(--primary-20-base, #f4f3ff);
  --primary-30: var(--primary-30-base, #ede9fe);
  --primary-40: var(--primary-40-base, #ddd6fe);
  --primary-50: var(--primary-50-base, #c4b5fd);
  --primary-100: var(--primary-100-base, #a78bfa);
  --primary-200: var(--primary-200-base, #8b5cf6);
  --primary-300: var(--primary-300-base, #7c3aed);
  --primary-400: var(--primary-400-base, #6d28d9);
  --primary-500: var(--primary-500-base, #5b21b6);
  --primary-600: var(--primary-600-base, #4c1d95);
  --primary-700: var(--primary-700-base, #3c1361);
  --primary-800: var(--primary-800-base, #2e1065);
  --primary-900: var(--primary-900-base, #1e1b4b);


  /* Secondary colors - Modern emerald green */
  --secondary: var(--secondary-base);
  --secondary-foreground: #ffffff;
  --secondary-bg: #f0fdf4;

  /* Secondary color variants - Emerald green */
--secondary-10:   var(--secondary-10-base,   #f0fdf4);
--secondary-20:   var(--secondary-20-base,   #dcfce7);
--secondary-30:   var(--secondary-30-base,   #bbf7d0);
--secondary-40:   var(--secondary-40-base,   #86efac);
--secondary-50:   var(--secondary-50-base,   #4ade80);
--secondary-100:  var(--secondary-100-base,  #22c55e);
--secondary-200:  var(--secondary-200-base,  #16a34a);
--secondary-300:  var(--secondary-300-base,  #15803d);
--secondary-400:  var(--secondary-400-base,  #166534);
--secondary-500:  var(--secondary-500-base,  #14532d);
--secondary-600:  var(--secondary-600-base,  #052e16);
--secondary-700:  var(--secondary-700-base,  #052e16);
--secondary-800:  var(--secondary-800-base,  #052e16);
--secondary-900:  var(--secondary-900-base,  #052e16);



  /* Accent - Modern amber for highlighting */
  --accent: var(--accent-base);
  --accent-foreground: #ffffff;
  --accent-bg: #fffbeb;

  /* Accent color variants - Amber */
--accent-10:   var(--accent-10-base,   #fffbeb);
--accent-20:   var(--accent-20-base,   #fef3c7);
--accent-30:   var(--accent-30-base,   #fde68a);
--accent-40:   var(--accent-40-base,   #fcd34d);
--accent-50:   var(--accent-50-base,   #fbbf24);
--accent-100:  var(--accent-100-base,  #f59e0b);
--accent-200:  var(--accent-200-base,  #d97706);
--accent-300:  var(--accent-300-base,  #b45309);
--accent-400:  var(--accent-400-base,  #92400e);
--accent-500:  var(--accent-500-base,  #78350f);
--accent-600:  var(--accent-600-base,  #451a03);
--accent-700:  var(--accent-700-base,  #451a03);
--accent-800:  var(--accent-800-base,  #451a03);
--accent-900:  var(--accent-900-base,  #451a03);



  /* Status colors - Modern and accessible */
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  /* Neutral/UI colors - Clean and modern */
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --input: #f1f5f9;
  --ring: #6366f1;

  /* Radius for consistent component styling */
  --radius: 0.5rem;
  }

  html.dark {
    /* Base colors - Modern dark theme */
  --background: #0f172a;
  --foreground: #f8fafc;

  /* UI Component backgrounds - Elevated dark surfaces */
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;

  /* Primary colors - Bright purple for dark mode */
  --primary: #8b5cf6;
  --primary-foreground: #ffffff;
  --primary-bg: #1e1b4b;

  /* Primary color variants for dark mode */
  --primary-10: var(--primary-10-base, #F8FAFD);
  --primary-20: var(--primary-20-base, #F2F5FA);
  --primary-30: var(--primary-30-base, #EAF0F7);
  --primary-40: var(--primary-40-base, #D9E4F0);
  --primary-50: var(--primary-50-base, #F5F9FF);
  --primary-100: var(--primary-100-base, #E8F1FE);
  --primary-200: var(--primary-200-base, #C9DFFC);
  --primary-300: var(--primary-300-base, #92BFFA);
  --primary-400: var(--primary-400-base, #5A9DF7);
  --primary-500: var(--primary-500-base, #1A73E8);
  --primary-600: var(--primary-600-base, #1259B8);
  --primary-700: var(--primary-700-base, #0D4287);
  --primary-800: var(--primary-800-base, #072C5E);
  --primary-900: var(--primary-900-base, #04172F);

  /* Secondary colors - Bright emerald for dark mode */
  --secondary: #22c55e;
  --secondary-foreground: #ffffff;
  --secondary-bg: #14532d;

  /* Secondary color variants for dark mode */
--secondary-10:   var(--primary-10-base,   #EBF9F3);
--secondary-20:   var(--primary-20-base,   #D7F4E7);
--secondary-30:   var(--primary-30-base,   #BFEEDC);
--secondary-40:   var(--primary-40-base,   #A6E8D0);
--secondary-50:   var(--primary-50-base,   #8EE2C4);
--secondary-100:  var(--primary-100-base,  #75DCB8);
--secondary-200:  var(--primary-200-base,  #5CD6AC);
--secondary-300:  var(--primary-300-base,  #43D0A0);
--secondary-400:  var(--primary-400-base,  #2ACB94);
--secondary-500:  var(--primary-500-base,  #36B37E);
--secondary-600:  var(--primary-600-base,  #2C9467);
--secondary-700:  var(--primary-700-base,  #227551);
--secondary-800:  var(--primary-800-base,  #18573A);
--secondary-900:  var(--primary-900-base,  #0E3824);


  /* Accent - Bright amber for dark mode */
  --accent: #fbbf24;
  --accent-foreground: #000000;
  --accent-bg: #78350f;

  /* Accent color variants for dark mode */
--tertiary-10:   var(--tertiary-10-base,   #FFF9E8);
--tertiary-20:   var(--tertiary-20-base,   #FFF2D1);
--tertiary-30:   var(--tertiary-30-base,   #FFE8AF);
--tertiary-40:   var(--tertiary-40-base,   #FFDD8C);
--tertiary-50:   var(--tertiary-50-base,   #FFD26A);
--tertiary-100:  var(--tertiary-100-base,  #FFC747);
--tertiary-200:  var(--tertiary-200-base,  #FFBC25);
--tertiary-300:  var(--tertiary-300-base,  #FBBC05);
--tertiary-400:  var(--tertiary-400-base,  #DB9F04);
--tertiary-500:  var(--tertiary-500-base,  #BB8403);
--tertiary-600:  var(--tertiary-600-base,  #9C6A02);
--tertiary-700:  var(--tertiary-700-base,  #7C5202);
--tertiary-800:  var(--tertiary-800-base,  #5C3B01);
--tertiary-900:  var(--tertiary-900-base,  #3D2501);


  /* Status colors - bright and accessible for dark mode */
  --success: #22c55e;
  --success-foreground: #ffffff;
  --warning: #fbbf24;
  --warning-foreground: #000000;
  --destructive: #f87171;
  --destructive-foreground: #ffffff;

  /* Neutral/UI colors - Dark mode optimized */
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --border: #475569;
  --input: #334155;
  --ring: #8b5cf6;
  }
